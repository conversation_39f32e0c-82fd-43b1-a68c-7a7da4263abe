<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Flat Coin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { background: white; padding: 20px; border-radius: 8px; max-width: 800px; margin: 0 auto; }
        .coin-container { display: flex; justify-content: center; margin: 20px 0; padding: 20px; background: #222; border-radius: 8px; }
        .coin-canvas { border: 1px solid #666; }
        .controls { text-align: center; margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; background: #007bff; color: white; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; font-family: monospace; font-size: 11px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .status { background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Debug Flat Coin</h1>
        
        <div class="coin-container">
            <canvas id="coinCanvas" width="300" height="300" class="coin-canvas"></canvas>
        </div>

        <div class="controls">
            <button id="testHeads" class="btn">Test Heads</button>
            <button id="testTails" class="btn">Test Tails</button>
            <button id="checkState" class="btn">Check Current State</button>
        </div>

        <div class="status" id="status">Loading...</div>
        <div class="debug" id="debug">Initializing...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        let debugElement = document.getElementById('debug');
        let statusElement = document.getElementById('status');

        function log(message) {
            console.log(message);
            debugElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        function checkCoinState() {
            if (!coinFlipper || !coinFlipper.coinRenderer) {
                log('CoinFlipper not ready');
                return;
            }

            const coin = coinFlipper.coinRenderer.coin;
            const rotX = coin.rotation.x * 180 / Math.PI;
            const rotY = coin.rotation.y * 180 / Math.PI;
            const rotZ = coin.rotation.z * 180 / Math.PI;
            const posX = coin.position.x;
            const posY = coin.position.y;
            const posZ = coin.position.z;
            const groundY = coinFlipper.coinRenderer.groundY;
            const coinHalfHeight = coinFlipper.coinRenderer.coinHalfHeight;
            const expectedY = groundY + coinHalfHeight;

            log('=== Current Coin State ===');
            log(`Rotation: X=${rotX.toFixed(1)}°, Y=${rotY.toFixed(1)}°, Z=${rotZ.toFixed(1)}°`);
            log(`Position: X=${posX.toFixed(3)}, Y=${posY.toFixed(3)}, Z=${posZ.toFixed(3)}`);
            log(`Ground Y: ${groundY.toFixed(3)}, Expected Y: ${expectedY.toFixed(3)}`);
            
            const isFlat = Math.abs(rotX) < 1 || Math.abs(rotX - 180) < 1;
            const isOnGround = Math.abs(posY - expectedY) < 0.01;
            
            log(`Is Flat: ${isFlat} (X rotation within 1° of 0° or 180°)`);
            log(`Is On Ground: ${isOnGround} (Y position within 0.01 of expected)`);
            
            if (isFlat && isOnGround) {
                log('✅ SUCCESS: Coin is flat and on ground!');
                statusElement.textContent = '✅ Coin is flat and on ground';
                statusElement.style.background = '#d4edda';
            } else {
                log('❌ ISSUE: Coin is not flat or not on ground');
                statusElement.textContent = '❌ Coin is not flat or not on ground';
                statusElement.style.background = '#f8d7da';
            }
        }

        async function init() {
            try {
                log('Initializing CoinFlipper...');
                statusElement.textContent = 'Initializing...';
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: false
                });

                await coinFlipper.ready();
                log('CoinFlipper ready!');

                await coinFlipper.startIdle();
                log('Idle animation started');
                statusElement.textContent = 'Ready - Idle animation running';

                setupEventListeners();
                checkCoinState();

            } catch (error) {
                log('Error: ' + error.message);
                statusElement.textContent = 'Error: ' + error.message;
                statusElement.style.background = '#f8d7da';
            }
        }

        function setupEventListeners() {
            document.getElementById('testHeads').onclick = () => testFlip('heads');
            document.getElementById('testTails').onclick = () => testFlip('tails');
            document.getElementById('checkState').onclick = checkCoinState;
        }

        async function testFlip(result) {
            try {
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(btn => btn.disabled = true);
                
                log(`\n=== Starting ${result} test ===`);
                statusElement.textContent = `Testing ${result}...`;

                await coinFlipper.stopIdle();
                log('Idle stopped, starting flip...');
                
                const flipResult = await coinFlipper.toss(result);
                
                log(`Flip completed! Result: ${flipResult}`);
                statusElement.textContent = `Flip completed: ${flipResult}`;
                
                // Wait a moment for settling to complete
                setTimeout(() => {
                    checkCoinState();
                    
                    setTimeout(async () => {
                        await coinFlipper.startIdle();
                        log('Idle restarted');
                        statusElement.textContent = 'Ready - Idle animation running';
                        buttons.forEach(btn => btn.disabled = false);
                    }, 1000);
                }, 500);

            } catch (error) {
                log('Error during flip: ' + error.message);
                statusElement.textContent = 'Error: ' + error.message;
                statusElement.style.background = '#f8d7da';
                document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
