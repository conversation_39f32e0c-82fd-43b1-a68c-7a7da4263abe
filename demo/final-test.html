<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Flat Coin Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { background: white; padding: 20px; border-radius: 8px; max-width: 700px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .coin-container { display: flex; justify-content: center; margin: 20px 0; padding: 20px; background: #333; border-radius: 8px; }
        .coin-canvas { border: 1px solid #666; border-radius: 4px; }
        .controls { text-align: center; margin: 20px 0; }
        .btn { padding: 12px 24px; margin: 8px; border: none; border-radius: 6px; background: #007bff; color: white; cursor: pointer; font-size: 16px; font-weight: 600; }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .result { background: #e9ecef; padding: 15px; border-radius: 6px; margin: 15px 0; text-align: center; font-size: 18px; font-weight: bold; }
        .result.success { background: #d4edda; color: #155724; border: 2px solid #28a745; }
        .result.error { background: #f8d7da; color: #721c24; border: 2px solid #dc3545; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 250px; overflow-y: auto; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Final Flat Coin Test</h1>
        <p>ทดสอบให้เหรียญนอนราบกับพื้นเมื่อหยุดหมุน</p>
        
        <div class="coin-container">
            <canvas id="coinCanvas" width="350" height="350" class="coin-canvas"></canvas>
        </div>

        <div class="controls">
            <button id="testHeads" class="btn">🪙 ทดสอบ หัว</button>
            <button id="testTails" class="btn">🪙 ทดสอบ ก้อย</button>
            <button id="testRandom" class="btn">🎲 ทดสอบ สุ่ม</button>
        </div>

        <div class="result" id="result">พร้อมทดสอบ</div>
        <div class="debug" id="debug">กำลังโหลด...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        let debugElement = document.getElementById('debug');
        let resultElement = document.getElementById('result');

        function log(message) {
            console.log(message);
            debugElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        function updateResult(message, isSuccess = null) {
            resultElement.textContent = message;
            resultElement.className = 'result';
            if (isSuccess === true) {
                resultElement.className += ' success';
            } else if (isSuccess === false) {
                resultElement.className += ' error';
            }
        }

        function checkFlatness() {
            if (!coinFlipper || !coinFlipper.coinRenderer) {
                return { isFlat: false, isOnGround: false, message: 'CoinFlipper not ready' };
            }

            const coin = coinFlipper.coinRenderer.coin;
            const rotX = coin.rotation.x * 180 / Math.PI;
            const rotY = coin.rotation.y * 180 / Math.PI;
            const rotZ = coin.rotation.z * 180 / Math.PI;
            const posY = coin.position.y;
            const groundY = coinFlipper.coinRenderer.groundY;
            const coinHalfHeight = coinFlipper.coinRenderer.coinHalfHeight;
            const expectedY = groundY + coinHalfHeight;

            // ตรวจสอบว่าเหรียญนอนแบน (rotation X ใกล้ 0° หรือ 180°)
            const isFlat = Math.abs(rotX) < 2 || Math.abs(rotX - 180) < 2;
            
            // ตรวจสอบว่าเหรียญอยู่บนพื้น
            const isOnGround = Math.abs(posY - expectedY) < 0.02;

            log(`=== Flatness Check ===`);
            log(`Rotation: X=${rotX.toFixed(1)}°, Y=${rotY.toFixed(1)}°, Z=${rotZ.toFixed(1)}°`);
            log(`Position Y: ${posY.toFixed(3)}, Expected: ${expectedY.toFixed(3)}, Diff: ${Math.abs(posY - expectedY).toFixed(3)}`);
            log(`Is Flat: ${isFlat} (X within 2° of 0° or 180°)`);
            log(`Is On Ground: ${isOnGround} (Y within 0.02 of expected)`);

            return { isFlat, isOnGround, rotX, rotY, rotZ, posY, expectedY };
        }

        async function init() {
            try {
                log('เริ่มต้น CoinFlipper...');
                updateResult('กำลังโหลด...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: false
                });

                await coinFlipper.ready();
                log('CoinFlipper พร้อมใช้งาน!');

                await coinFlipper.startIdle();
                log('เริ่ม idle animation');
                updateResult('พร้อมทดสอบ');

                setupEventListeners();

            } catch (error) {
                log('ข้อผิดพลาด: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message, false);
            }
        }

        function setupEventListeners() {
            document.getElementById('testHeads').onclick = () => testFlip('heads');
            document.getElementById('testTails').onclick = () => testFlip('tails');
            document.getElementById('testRandom').onclick = () => testFlip();
        }

        async function testFlip(result = null) {
            try {
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(btn => btn.disabled = true);
                
                const testType = result || 'สุ่ม';
                log(`\n=== เริ่มทดสอบ ${testType} ===`);
                updateResult(`กำลังทดสอบ ${testType}...`);

                await coinFlipper.stopIdle();
                log('หยุด idle animation');
                
                const flipResult = await coinFlipper.toss(result);
                log(`ผลลัพธ์การทอย: ${flipResult}`);
                
                // รอให้ settling animation เสร็จสิ้น
                setTimeout(() => {
                    const check = checkFlatness();
                    
                    if (check.isFlat && check.isOnGround) {
                        log('✅ สำเร็จ! เหรียญนอนราบบนพื้น');
                        updateResult(`✅ สำเร็จ! ผลลัพธ์: ${flipResult} - เหรียญนอนราบบนพื้น`, true);
                    } else {
                        log('❌ ล้มเหลว! เหรียญไม่นอนราบหรือไม่อยู่บนพื้น');
                        let issues = [];
                        if (!check.isFlat) issues.push('ไม่ราบ');
                        if (!check.isOnGround) issues.push('ไม่อยู่บนพื้น');
                        updateResult(`❌ ล้มเหลว! ผลลัพธ์: ${flipResult} - ${issues.join(', ')}`, false);
                    }
                    
                    // เริ่ม idle อีกครั้งหลังจาก 2 วินาที
                    setTimeout(async () => {
                        await coinFlipper.startIdle();
                        log('เริ่ม idle animation อีกครั้ง');
                        buttons.forEach(btn => btn.disabled = false);
                    }, 2000);
                    
                }, 1000); // รอ 1 วินาทีให้ settling เสร็จ

            } catch (error) {
                log('ข้อผิดพลาดในการทดสอบ: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message, false);
                document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
