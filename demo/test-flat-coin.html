<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Flat Coin Landing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .coin-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: #333;
            border-radius: 10px;
        }
        .coin-canvas {
            border: 2px solid #666;
            border-radius: 8px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 6px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Test Flat Coin Landing</h1>
        <p>This test verifies that the coin lies completely flat when the animation completes.</p>
        
        <div class="coin-container">
            <canvas 
                id="coinCanvas"
                width="400" 
                height="400"
                class="coin-canvas"
            ></canvas>
        </div>

        <div class="controls">
            <button id="testHeads" class="btn">Test Heads (Should lie flat)</button>
            <button id="testTails" class="btn">Test Tails (Should lie flat)</button>
            <button id="testRandom" class="btn">Test Random</button>
            <button id="startIdle" class="btn">Start Idle</button>
            <button id="stopIdle" class="btn">Stop Idle</button>
        </div>

        <div class="status">
            <h3>Status</h3>
            <p><strong>State:</strong> <span id="status">Loading...</span></p>
            <p><strong>Last Result:</strong> <span id="lastResult">None</span></p>
        </div>

        <div class="debug-info" id="debugInfo">
            Initializing...
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- CoinFlipper Module -->
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        let debugInfo = document.getElementById('debugInfo');
        let statusElement = document.getElementById('status');
        let lastResultElement = document.getElementById('lastResult');

        function log(message) {
            console.log(message);
            debugInfo.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }

        async function init() {
            try {
                log('Initializing CoinFlipper...');
                statusElement.textContent = 'Initializing...';

                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: true
                });

                await coinFlipper.ready();
                log('CoinFlipper ready!');

                await coinFlipper.startIdle();
                log('Idle animation started');
                statusElement.textContent = 'Ready (Idle)';

                setupEventListeners();

            } catch (error) {
                log('Error: ' + error.message);
                statusElement.textContent = 'Error';
            }
        }

        function setupEventListeners() {
            document.getElementById('testHeads').onclick = () => testFlip('heads');
            document.getElementById('testTails').onclick = () => testFlip('tails');
            document.getElementById('testRandom').onclick = () => testFlip();
            
            document.getElementById('startIdle').onclick = async () => {
                await coinFlipper.startIdle();
                statusElement.textContent = 'Idle';
                log('Idle started');
            };
            
            document.getElementById('stopIdle').onclick = async () => {
                await coinFlipper.stopIdle();
                statusElement.textContent = 'Stopped';
                log('Idle stopped');
            };
        }

        async function testFlip(result = null) {
            try {
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(btn => btn.disabled = true);
                
                statusElement.textContent = 'Flipping...';
                log(`Starting flip test${result ? ' (forced ' + result + ')' : ' (random)'}...`);

                await coinFlipper.stopIdle();
                const flipResult = await coinFlipper.toss(result);
                
                log(`Flip completed! Result: ${flipResult}`);
                log('Checking if coin is lying flat...');
                
                // Check coin position and rotation
                if (coinFlipper.coinRenderer) {
                    const coin = coinFlipper.coinRenderer.coin;
                    const rotX = coin.rotation.x;
                    const rotY = coin.rotation.y;
                    const rotZ = coin.rotation.z;
                    const posY = coin.position.y;
                    const groundY = coinFlipper.coinRenderer.groundY;
                    const coinHalfHeight = coinFlipper.coinRenderer.coinHalfHeight;
                    
                    log(`Final rotation: X=${(rotX * 180 / Math.PI).toFixed(1)}°, Y=${(rotY * 180 / Math.PI).toFixed(1)}°, Z=${(rotZ * 180 / Math.PI).toFixed(1)}°`);
                    log(`Final position Y: ${posY.toFixed(3)}, Ground Y: ${groundY}, Expected Y: ${(groundY + coinHalfHeight).toFixed(3)}`);
                    
                    const isFlat = Math.abs(rotX) < 0.01 || Math.abs(rotX - Math.PI) < 0.01;
                    const isOnGround = Math.abs(posY - (groundY + coinHalfHeight)) < 0.001;
                    
                    log(`Is flat: ${isFlat}, Is on ground: ${isOnGround}`);
                    
                    if (isFlat && isOnGround) {
                        log('✅ SUCCESS: Coin is lying flat on the ground!');
                    } else {
                        log('❌ ISSUE: Coin is not properly flat or positioned!');
                    }
                }
                
                statusElement.textContent = 'Completed';
                lastResultElement.textContent = flipResult;
                
                setTimeout(async () => {
                    await coinFlipper.startIdle();
                    statusElement.textContent = 'Idle';
                    buttons.forEach(btn => btn.disabled = false);
                }, 2000);

            } catch (error) {
                log('Error during flip: ' + error.message);
                statusElement.textContent = 'Error';
                document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
